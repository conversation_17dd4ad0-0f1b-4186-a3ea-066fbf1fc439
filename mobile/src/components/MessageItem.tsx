import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';

interface MessageItemProps {
  message?: any;
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  if (!message) return null;

  if (message.type === 'clear_chat') {
    return (
      <View style={styles.systemMessageContainer}>
        <View style={[
          styles.systemMessageContent,
          message.status === 'sending' && styles.sendingGradient
        ]}>
          <Text style={styles.systemMessageText}>
            Chat cleared by {message.is_mine ? 'you' : message.sender_username}
          </Text>
          <Text style={styles.messageTime}>{formatTime(message.timestamp)}</Text>
        </View>
      </View>
    );
  }

  if (message.type === 'typing') return null;

  const renderStatusIcon = () => {
    if (!message.isMine && !message.is_mine) return null;

    switch (message.status) {
      case 'sending':
        return (
          <View style={styles.statusContainer}>
            <ActivityIndicator size="small" color="#999" />
          </View>
        );
      case 'sent':
        return (
          <View style={styles.statusContainer}>
            <Text style={styles.singleTick}>✓</Text>
          </View>
        );
      case 'delivered':
      case 'read':
        return (
          <View style={styles.statusContainer}>
            <Text style={styles.doubleTick}>✓✓</Text>
          </View>
        );
      default:
        return null;
    }
  };

  const isMine = message.isMine || message.is_mine;

  return (
    <View style={[
      styles.messageContainer,
      isMine ? styles.sentMessage : styles.receivedMessage,
      isLastInGroup && styles.lastInGroup
    ]}>
      <View style={[
        styles.messageContent,
        isMine ? styles.sentContent : styles.receivedContent,
        message.status === 'sending' && styles.sendingGradient
      ]}>
        <Text style={[
          styles.messageText,
          isMine ? styles.sentText : styles.receivedText
        ]}>
          {message.message}
        </Text>
        <View style={styles.messageInfo}>
          <Text style={[
            styles.messageTime,
            isMine ? styles.sentTime : styles.receivedTime
          ]}>
            {formatTime(message.timestamp)}
          </Text>
          {renderStatusIcon()}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: 2,
    marginHorizontal: 12,
  },
  sentMessage: {
    alignItems: 'flex-end',
  },
  receivedMessage: {
    alignItems: 'flex-start',
  },
  lastInGroup: {
    marginBottom: 8,
  },
  messageContent: {
    maxWidth: '80%',
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  sentContent: {
    backgroundColor: '#2196F3',
  },
  receivedContent: {
    backgroundColor: '#E5E5EA',
  },
  sendingGradient: {
    opacity: 0.7,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  sentText: {
    color: '#ffffff',
  },
  receivedText: {
    color: '#000000',
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  messageTime: {
    fontSize: 12,
    marginRight: 4,
  },
  sentTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  receivedTime: {
    color: '#666',
  },
  statusContainer: {
    marginLeft: 4,
  },
  singleTick: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
  },
  doubleTick: {
    fontSize: 12,
    color: '#4CAF50',
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: 8,
  },
  systemMessageContent: {
    backgroundColor: '#F0F0F0',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignItems: 'center',
  },
  systemMessageText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default MessageItem;
